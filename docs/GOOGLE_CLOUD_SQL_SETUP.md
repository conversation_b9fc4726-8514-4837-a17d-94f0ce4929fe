# Google Cloud SQL Proxy Setup for GitHub Actions

Este documento explica como configurar o Google Cloud SQL Proxy para autenticação segura nas migrations do GitHub Actions.

## 🔧 Configuração Necessária

### 1. Service Account no Google Cloud

1. **Acesse o Google Cloud Console**
2. **Vá para IAM & Admin > Service Accounts**
3. **Crie uma nova Service Account** ou use uma existente
4. **Adicione as seguintes roles**:
   - `Cloud SQL Client`
   - `Cloud SQL Editor` (se precisar criar databases)

### 2. Gerar Chave da Service Account

1. **Na Service Account criada**, clique em "Actions" > "Manage Keys"
2. **Clique em "Add Key" > "Create New Key"**
3. **Escolha formato JSON**
4. **Baixe o arquivo JSON** (guarde com segurança)

### 3. Configurar Secrets no GitHub

Adicione os seguintes secrets no seu repositório GitHub:

#### Secrets Obrigatórios:

```bash
# Service Account Key (conteúdo completo do arquivo JSON)
GCP_SA_KEY={"type":"service_account","project_id":"..."}

# Connection Name da instância Cloud SQL
# Formato: project-id:region:instance-name
GCP_SQL_INSTANCE_CONNECTION_NAME=meu-projeto:us-central1:minha-instancia

# Credenciais do banco
GCP_DB_NAME=high-capital
GCP_DB_USER=postgres-high-capital
GCP_DB_PASSWORD=YUli3z5([ZEJ%UOZ
```

#### Como encontrar o Connection Name:

1. **Acesse Cloud SQL no Console**
2. **Clique na sua instância**
3. **Na aba "Overview"**, copie o "Connection name"
4. **Formato**: `project-id:region:instance-name`

### 4. Configurar Cloud SQL Instance

Certifique-se de que sua instância Cloud SQL está configurada para:

1. **Permitir conexões via Cloud SQL Proxy**
2. **Ter o usuário e database configurados**
3. **Service Account tem acesso à instância**

## 🚀 Como Funciona

### Fluxo do Workflow:

1. **Autentica** com Google Cloud usando a Service Account
2. **Instala** o Cloud SQL Proxy
3. **Inicia** o proxy conectando à instância Cloud SQL
4. **Executa** as migrations via proxy (localhost:5432)
5. **Proxy** faz a autenticação segura com Cloud SQL

### Vantagens:

- ✅ **Seguro**: Não expõe IPs públicos
- ✅ **Autenticado**: Usa Service Account do GCP
- ✅ **Criptografado**: Conexão segura via proxy
- ✅ **Simples**: Transparente para a aplicação

## 🔍 Troubleshooting

### Erro: "failed to connect to instance"

```bash
# Verifique se o Connection Name está correto
GCP_SQL_INSTANCE_CONNECTION_NAME=projeto:regiao:instancia
```

### Erro: "permission denied"

```bash
# Verifique se a Service Account tem as roles:
- Cloud SQL Client
- Cloud SQL Editor (se necessário)
```

### Erro: "database does not exist"

```bash
# Certifique-se de que o database existe:
GCP_DB_NAME=nome_do_database_existente
```

### Erro: "authentication failed"

```bash
# Verifique as credenciais:
GCP_DB_USER=usuario_correto
GCP_DB_PASSWORD=senha_correta
```

## 📋 Checklist de Configuração

- [ ] Service Account criada com roles corretas
- [ ] Chave JSON da Service Account baixada
- [ ] Secret `GCP_SA_KEY` configurado no GitHub
- [ ] Secret `GCP_SQL_INSTANCE_CONNECTION_NAME` configurado
- [ ] Secrets de database (`GCP_DB_NAME`, `GCP_DB_USER`, `GCP_DB_PASSWORD`) configurados
- [ ] Instância Cloud SQL permite conexões via proxy
- [ ] Database e usuário existem na instância

## 🔗 Links Úteis

- [Cloud SQL Proxy Documentation](https://cloud.google.com/sql/docs/postgres/sql-proxy)
- [Service Account Setup](https://cloud.google.com/iam/docs/creating-managing-service-accounts)
- [GitHub Actions Secrets](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
