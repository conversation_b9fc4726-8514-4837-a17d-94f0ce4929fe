#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Helper script para facilitar o uso do sistema de versionamento automático

.DESCRIPTION
    Este script fornece uma interface simplificada para os diferentes tipos de versionamento
    disponíveis no projeto HighCapital.Core

.PARAMETER Action
    Ação a ser executada: check, increment, semantic, git-based, help

.PARAMETER Type
    Tipo de incremento: patch, minor, major, auto

.PARAMETER DryRun
    Executa em modo dry-run (apenas visualização)

.EXAMPLE
    .\scripts\version-helper.ps1 -Action check
    Mostra a versão atual

.EXAMPLE
    .\scripts\version-helper.ps1 -Action increment -Type patch
    Incrementa a versão patch

.EXAMPLE
    .\scripts\version-helper.ps1 -Action semantic -DryRun
    Executa análise semântica em modo dry-run
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("check", "increment", "semantic", "git-based", "help")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("patch", "minor", "major", "auto")]
    [string]$Type = "auto",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateTag
)

$ProjectPath = "src/Core/Core.csproj"
$ScriptsPath = Split-Path -Parent $MyInvocation.MyCommand.Path

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorOutput "HighCapital.Core - Sistema de Versionamento Automatico" "Cyan"
    Write-ColorOutput "============================================================" "Gray"
    Write-ColorOutput ""
    
    Write-ColorOutput "ACOES DISPONIVEIS:" "Yellow"
    Write-ColorOutput "  check      - Mostra a versao atual do projeto" "White"
    Write-ColorOutput "  increment  - Incrementa versao manualmente" "White"
    Write-ColorOutput "  semantic   - Analise semantica de commits" "White"
    Write-ColorOutput "  git-based  - Versionamento baseado em Git tags" "White"
    Write-ColorOutput "  help       - Mostra esta ajuda" "White"
    Write-ColorOutput ""
    
    Write-ColorOutput "TIPOS DE INCREMENTO:" "Yellow"
    Write-ColorOutput "  patch  - 1.0.4 -> 1.0.5 (correcoes)" "Blue"
    Write-ColorOutput "  minor  - 1.0.4 -> 1.1.0 (novas funcionalidades)" "Green"
    Write-ColorOutput "  major  - 1.0.4 -> 2.0.0 (breaking changes)" "Red"
    Write-ColorOutput "  auto   - Determina automaticamente baseado em commits" "Cyan"
    Write-ColorOutput ""
    
    Write-ColorOutput "EXEMPLOS DE USO:" "Yellow"
    Write-ColorOutput "  # Verificar versao atual" "Gray"
    Write-ColorOutput "  .\scripts\version-helper.ps1 -Action check" "White"
    Write-ColorOutput ""
    Write-ColorOutput "  # Incrementar patch version" "Gray"
    Write-ColorOutput "  .\scripts\version-helper.ps1 -Action increment -Type patch" "White"
    Write-ColorOutput ""
    Write-ColorOutput "  # Analise semantica (dry-run)" "Gray"
    Write-ColorOutput "  .\scripts\version-helper.ps1 -Action semantic -DryRun" "White"
    Write-ColorOutput ""
    Write-ColorOutput "  # Versionamento baseado em Git tags" "Gray"
    Write-ColorOutput "  .\scripts\version-helper.ps1 -Action git-based -CreateTag" "White"
    Write-ColorOutput ""
    
    Write-ColorOutput "CONVENTIONAL COMMITS:" "Yellow"
    Write-ColorOutput "  feat: nova funcionalidade    -> minor" "Green"
    Write-ColorOutput "  fix: correcao de bug         -> patch" "Blue"
    Write-ColorOutput "  feat!: breaking change      -> major" "Red"
    Write-ColorOutput "  docs: documentacao           -> nenhum" "Gray"
    Write-ColorOutput "  chore: manutencao            -> nenhum" "Gray"
    Write-ColorOutput ""
}

function Get-CurrentVersion {
    if (-not (Test-Path $ProjectPath)) {
        Write-ColorOutput "❌ Arquivo do projeto não encontrado: $ProjectPath" "Red"
        return $null
    }
    
    try {
        [xml]$projectXml = Get-Content $ProjectPath
        $version = $projectXml.Project.PropertyGroup.Version
        return $version
    }
    catch {
        Write-ColorOutput "❌ Erro ao ler versão: $($_.Exception.Message)" "Red"
        return $null
    }
}

function Show-CurrentVersion {
    Write-ColorOutput "HighCapital.Core - Versao Atual" "Cyan"
    Write-ColorOutput "========================================" "Gray"

    $version = Get-CurrentVersion
    if ($version) {
        Write-ColorOutput "Versao: $version" "Green"
        Write-ColorOutput "Arquivo: $ProjectPath" "Gray"
        
        # Mostrar informacoes adicionais do Git se disponivel
        try {
            $gitBranch = git branch --show-current 2>$null
            $gitCommit = git rev-parse --short HEAD 2>$null
            $gitTags = git tag --points-at HEAD 2>$null
            
            if ($gitBranch) {
                Write-ColorOutput "Branch: $gitBranch" "Gray"
            }
            if ($gitCommit) {
                Write-ColorOutput "Commit: $gitCommit" "Gray"
            }
            if ($gitTags) {
                Write-ColorOutput "Tags: $($gitTags -join ', ')" "Gray"
            }
        }
        catch {
            # Git nao disponivel ou nao e um repositorio Git
        }
    }
    Write-ColorOutput ""
}

function Execute-VersionScript {
    param(
        [string]$ScriptName,
        [hashtable]$Parameters
    )

    $scriptPath = Join-Path $ScriptsPath $ScriptName

    if (-not (Test-Path $scriptPath)) {
        Write-ColorOutput "Script nao encontrado: $scriptPath" "Red"
        return $false
    }

    try {
        Write-ColorOutput "Executando: $ScriptName" "Cyan"
        Write-ColorOutput "Parametros: $($Parameters.Keys -join ', ')" "Gray"
        Write-ColorOutput ""

        & $scriptPath @Parameters
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput ""
            Write-ColorOutput "Execucao concluida com sucesso!" "Green"
            return $true
        }
        else {
            Write-ColorOutput ""
            Write-ColorOutput "Execucao falhou com codigo: $LASTEXITCODE" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "Erro na execucao: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Main execution
try {
    switch ($Action) {
        "help" {
            Show-Help
        }
        
        "check" {
            Show-CurrentVersion
        }
        
        "increment" {
            $params = @{
                VersionType = $Type
            }
            if ($DryRun) { $params.DryRun = $true }
            if ($CreateTag) { $params.CreateTag = $true }

            Execute-VersionScript -ScriptName "increment-version.ps1" -Parameters $params
        }
        
        "semantic" {
            $params = @{}
            if ($DryRun) { $params.DryRun = $true }
            if ($CreateTag) { $params.CreateTag = $true }

            Execute-VersionScript -ScriptName "semantic-version.ps1" -Parameters $params
        }
        
        "git-based" {
            $params = @{}
            if ($DryRun) { $params.DryRun = $true }
            if ($CreateTag) { $params.CreateTag = $true }

            Execute-VersionScript -ScriptName "git-version.ps1" -Parameters $params
        }
        
        default {
            Write-ColorOutput "Acao invalida: $Action" "Red"
            Write-ColorOutput "Use -Action help para ver as opcoes disponiveis" "Yellow"
            exit 1
        }
    }
}
catch {
    Write-ColorOutput "Erro: $($_.Exception.Message)" "Red"
    exit 1
}
