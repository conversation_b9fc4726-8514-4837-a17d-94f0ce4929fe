param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "src/Core/Core.csproj",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [int]$CommitDepth = 50,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateTag,
    
    [Parameter(Mandatory=$false)]
    [string]$TagPrefix = "v"
)

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Get-ConventionalCommits {
    param([int]$Depth)
    
    try {
        $commits = git log --oneline -$Depth --pretty=format:"%s" 2>$null
        
        if ($LASTEXITCODE -eq 0 -and $commits) {
            return $commits
        }
        else {
            Write-ColorOutput "No commits found or Git not available" "Yellow"
            return @()
        }
    }
    catch {
        Write-ColorOutput "Error reading commits: $($_.Exception.Message)" "Red"
        return @()
    }
}

function Parse-ConventionalCommit {
    param([string]$CommitMessage)
    
    # Conventional Commits format: type(scope): description
    # Examples:
    # feat: add new feature
    # fix: resolve bug
    # feat!: breaking change
    # feat(api)!: breaking change in api scope
    
    $result = @{
        Type = $null
        Scope = $null
        Description = $null
        IsBreaking = $false
        VersionImpact = "none"
    }
    
    # Pattern for conventional commits
    $pattern = '^(?<type>\w+)(?:\((?<scope>[^)]+)\))?(?<breaking>!)?:\s*(?<description>.+)$'
    
    if ($CommitMessage -match $pattern) {
        $result.Type = $matches['type'].ToLower()
        $result.Scope = $matches['scope']
        $result.Description = $matches['description']
        $result.IsBreaking = $matches['breaking'] -eq '!'
        
        # Determine version impact based on type and breaking change
        if ($result.IsBreaking) {
            $result.VersionImpact = "major"
        }
        elseif ($result.Type -in @('feat', 'feature')) {
            $result.VersionImpact = "minor"
        }
        elseif ($result.Type -in @('fix', 'bugfix', 'patch', 'perf', 'refactor')) {
            $result.VersionImpact = "patch"
        }
        else {
            # Types like docs, style, test, chore don't increment version
            $result.VersionImpact = "none"
        }
    }
    else {
        # Non-conventional commit - try to infer from keywords
        $result = Analyze-NonConventionalCommit -CommitMessage $CommitMessage
    }
    
    return $result
}

function Analyze-NonConventionalCommit {
    param([string]$CommitMessage)
    
    $result = @{
        Type = "unknown"
        Scope = $null
        Description = $CommitMessage
        IsBreaking = $false
        VersionImpact = "patch"  # Default for non-conventional commits
    }
    
    $message = $CommitMessage.ToLower()
    
    # Check for breaking change indicators
    $breakingPatterns = @(
        'breaking\s+change',
        'breaking:',
        '\[breaking\]',
        'major\s+change',
        '\bmajor\b.*\bchange\b'
    )
    
    foreach ($pattern in $breakingPatterns) {
        if ($message -match $pattern) {
            $result.IsBreaking = $true
            $result.VersionImpact = "major"
            $result.Type = "breaking"
            return $result
        }
    }
    
    # Check for feature indicators
    $featurePatterns = @(
        '\bfeat\b',
        '\bfeature\b',
        '\badd\b.*\bfeature\b',
        '\bnew\b.*\bfeature\b',
        '\bimplement\b',
        '\bintroduce\b'
    )
    
    foreach ($pattern in $featurePatterns) {
        if ($message -match $pattern) {
            $result.Type = "feature"
            $result.VersionImpact = "minor"
            return $result
        }
    }
    
    # Check for fix indicators
    $fixPatterns = @(
        '\bfix\b',
        '\bbug\b',
        '\bresolve\b',
        '\bpatch\b',
        '\bcorrect\b',
        '\brepair\b'
    )
    
    foreach ($pattern in $fixPatterns) {
        if ($message -match $pattern) {
            $result.Type = "fix"
            $result.VersionImpact = "patch"
            return $result
        }
    }
    
    # Check for non-version impacting changes
    $nonVersionPatterns = @(
        '\bdocs?\b',
        '\bdocument\b',
        '\bstyle\b',
        '\bformat\b',
        '\btest\b',
        '\bchore\b',
        '\bci\b',
        '\bbuild\b'
    )
    
    foreach ($pattern in $nonVersionPatterns) {
        if ($message -match $pattern) {
            $result.Type = "chore"
            $result.VersionImpact = "none"
            return $result
        }
    }
    
    return $result
}

function Get-HighestVersionImpact {
    param([array]$Commits)
    
    $impacts = @{
        "major" = 3
        "minor" = 2
        "patch" = 1
        "none" = 0
    }
    
    $highestImpact = "none"
    $highestValue = 0
    
    $stats = @{
        major = 0
        minor = 0
        patch = 0
        none = 0
        total = $Commits.Count
    }
    
    Write-ColorOutput "Analyzing $($Commits.Count) commits for semantic versioning..." "Cyan"
    
    foreach ($commit in $Commits) {
        $parsed = Parse-ConventionalCommit -CommitMessage $commit
        $stats[$parsed.VersionImpact]++
        
        if ($impacts[$parsed.VersionImpact] -gt $highestValue) {
            $highestValue = $impacts[$parsed.VersionImpact]
            $highestImpact = $parsed.VersionImpact
        }
        
        $color = switch ($parsed.VersionImpact) {
            "major" { "Red" }
            "minor" { "Green" }
            "patch" { "Blue" }
            "none" { "Gray" }
        }
        
        Write-ColorOutput "  [$($parsed.VersionImpact.ToUpper())] $($parsed.Type): $($parsed.Description)" $color
    }
    
    Write-ColorOutput "" "White"
    Write-ColorOutput "Version Impact Summary:" "Cyan"
    Write-ColorOutput "  Major (breaking): $($stats.major)" "Red"
    Write-ColorOutput "  Minor (features): $($stats.minor)" "Green"
    Write-ColorOutput "  Patch (fixes): $($stats.patch)" "Blue"
    Write-ColorOutput "  None (chores): $($stats.none)" "Gray"
    Write-ColorOutput "" "White"
    
    return $highestImpact
}

function Get-CurrentVersion {
    param([string]$ProjectPath)
    
    if (-not (Test-Path $ProjectPath)) {
        throw "Project file not found: $ProjectPath"
    }
    
    [xml]$projectXml = Get-Content $ProjectPath
    $versionNode = $projectXml.Project.PropertyGroup.Version
    
    if (-not $versionNode) {
        throw "Version property not found in project file"
    }
    
    return $versionNode
}

function Get-NextSemanticVersion {
    param(
        [string]$CurrentVersion,
        [string]$VersionImpact
    )
    
    if ($VersionImpact -eq "none") {
        Write-ColorOutput "No version-impacting changes found, keeping current version" "Yellow"
        return $CurrentVersion
    }
    
    if (-not ($CurrentVersion -match '^(\d+)\.(\d+)\.(\d+)(?:-(.+))?$')) {
        throw "Invalid version format: $CurrentVersion. Expected format: x.y.z or x.y.z-suffix"
    }
    
    $major = [int]$matches[1]
    $minor = [int]$matches[2]
    $patch = [int]$matches[3]
    $suffix = $matches[4]
    
    switch ($VersionImpact) {
        "major" {
            $major++
            $minor = 0
            $patch = 0
        }
        "minor" {
            $minor++
            $patch = 0
        }
        "patch" {
            $patch++
        }
    }
    
    $newVersion = "$major.$minor.$patch"
    if ($suffix) {
        $newVersion += "-$suffix"
    }
    
    return $newVersion
}

function Update-ProjectVersion {
    param(
        [string]$ProjectPath,
        [string]$NewVersion
    )
    
    [xml]$projectXml = Get-Content $ProjectPath
    $oldVersion = $projectXml.Project.PropertyGroup.Version
    
    if (-not $DryRun) {
        $projectXml.Project.PropertyGroup.Version = $NewVersion
        $projectXml.Save((Resolve-Path $ProjectPath))
        Write-ColorOutput "Updated version from $oldVersion to $NewVersion" "Green"
    }
    else {
        Write-ColorOutput "DRY RUN: Would update version from $oldVersion to $NewVersion" "Yellow"
    }
}

# Main execution
try {
    Write-ColorOutput "Starting Semantic Versioning analysis..." "Cyan"
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN MODE - No changes will be made" "Yellow"
    }
    
    # Get recent commits
    $commits = Get-ConventionalCommits -Depth $CommitDepth
    
    if ($commits.Count -eq 0) {
        Write-ColorOutput "No commits found, exiting" "Yellow"
        exit 0
    }
    
    # Analyze commits for version impact
    $versionImpact = Get-HighestVersionImpact -Commits $commits
    
    # Get current version
    $currentVersion = Get-CurrentVersion -ProjectPath $ProjectPath
    
    # Calculate next version
    $nextVersion = Get-NextSemanticVersion -CurrentVersion $currentVersion -VersionImpact $versionImpact
    
    Write-ColorOutput "Current version: $currentVersion" "White"
    Write-ColorOutput "Version impact: $versionImpact" "White"
    Write-ColorOutput "Next version: $nextVersion" "Green"
    
    # Update version if there are changes
    if ($nextVersion -ne $currentVersion) {
        Update-ProjectVersion -ProjectPath $ProjectPath -NewVersion $nextVersion
        
        if ($CreateTag -and -not $DryRun) {
            git tag "$TagPrefix$nextVersion"
            Write-ColorOutput "Created tag: $TagPrefix$nextVersion" "Green"
        }
    }
    else {
        Write-ColorOutput "No version change needed" "Yellow"
    }
    
    Write-ColorOutput "Semantic versioning completed!" "Green"
    
    # Output for CI/CD
    Write-Output "NEW_VERSION=$nextVersion"
    Write-Output "VERSION_IMPACT=$versionImpact"
    Write-Output "VERSION_CHANGED=$($nextVersion -ne $currentVersion)"
}
catch {
    Write-ColorOutput "Error: $($_.Exception.Message)" "Red"
    exit 1
}
