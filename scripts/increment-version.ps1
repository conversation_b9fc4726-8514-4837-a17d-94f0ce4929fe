param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("patch", "minor", "major", "auto")]
    [string]$VersionType = "patch",
    
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "src/Core/Core.csproj",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseGitTags,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateTag
)

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Get-CurrentVersion {
    param([string]$ProjectPath)
    
    if (-not (Test-Path $ProjectPath)) {
        throw "Project file not found: $ProjectPath"
    }
    
    [xml]$projectXml = Get-Content $ProjectPath
    $versionNode = $projectXml.Project.PropertyGroup.Version
    
    if (-not $versionNode) {
        throw "Version property not found in project file"
    }
    
    return $versionNode
}

function Get-VersionFromGitTags {
    try {
        $latestTag = git describe --tags --abbrev=0 2>$null
        if ($LASTEXITCODE -eq 0 -and $latestTag) {
            # Remove 'v' prefix if present
            $version = $latestTag -replace '^v', ''
            Write-ColorOutput "Latest Git tag version: $version" "Cyan"
            return $version
        }
    }
    catch {
        Write-ColorOutput "No Git tags found, using project file version" "Yellow"
    }
    return $null
}

function Get-NextVersion {
    param(
        [string]$CurrentVersion,
        [string]$VersionType
    )
    
    if (-not ($CurrentVersion -match '^(\d+)\.(\d+)\.(\d+)(?:-(.+))?$')) {
        throw "Invalid version format: $CurrentVersion. Expected format: x.y.z or x.y.z-suffix"
    }
    
    $major = [int]$matches[1]
    $minor = [int]$matches[2]
    $patch = [int]$matches[3]
    $suffix = $matches[4]
    
    switch ($VersionType) {
        "major" {
            $major++
            $minor = 0
            $patch = 0
        }
        "minor" {
            $minor++
            $patch = 0
        }
        "patch" {
            $patch++
        }
        "auto" {
            # Analyze recent commits to determine version type
            $versionType = Get-AutoVersionType
            return Get-NextVersion -CurrentVersion $CurrentVersion -VersionType $versionType
        }
    }
    
    $newVersion = "$major.$minor.$patch"
    if ($suffix) {
        $newVersion += "-$suffix"
    }
    
    return $newVersion
}

function Get-AutoVersionType {
    # Get recent commit messages
    $commits = git log --oneline -10 --pretty=format:"%s" 2>$null
    
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "Could not read git commits, defaulting to patch" "Yellow"
        return "patch"
    }
    
    $breakingChanges = $commits | Where-Object { $_ -match '\b(BREAKING|breaking change|major)\b' }
    $features = $commits | Where-Object { $_ -match '\b(feat|feature|add|new)\b' }
    
    if ($breakingChanges) {
        Write-ColorOutput "Breaking changes detected, incrementing major version" "Red"
        return "major"
    }
    elseif ($features) {
        Write-ColorOutput "New features detected, incrementing minor version" "Green"
        return "minor"
    }
    else {
        Write-ColorOutput "Bug fixes/patches detected, incrementing patch version" "Blue"
        return "patch"
    }
}

function Update-ProjectVersion {
    param(
        [string]$ProjectPath,
        [string]$NewVersion
    )
    
    [xml]$projectXml = Get-Content $ProjectPath
    $versionNode = $projectXml.Project.PropertyGroup.Version
    $oldVersion = $versionNode
    
    $versionNode = $NewVersion
    $projectXml.Project.PropertyGroup.Version = $NewVersion
    
    if (-not $DryRun) {
        $projectXml.Save((Resolve-Path $ProjectPath))
        Write-ColorOutput "Updated version from $oldVersion to $NewVersion in $ProjectPath" "Green"
    }
    else {
        Write-ColorOutput "DRY RUN: Would update version from $oldVersion to $NewVersion in $ProjectPath" "Yellow"
    }
}

function Create-GitTag {
    param([string]$Version)
    
    $tagName = "v$Version"
    
    if (-not $DryRun) {
        git tag $tagName
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "Created Git tag: $tagName" "Green"
            Write-ColorOutput "To push the tag, run: git push origin $tagName" "Cyan"
        }
        else {
            Write-ColorOutput "Failed to create Git tag: $tagName" "Red"
        }
    }
    else {
        Write-ColorOutput "DRY RUN: Would create Git tag: $tagName" "Yellow"
    }
}

# Main execution
try {
    Write-ColorOutput "Starting version increment process..." "Cyan"
    Write-ColorOutput "Version type: $VersionType" "White"
    Write-ColorOutput "Project path: $ProjectPath" "White"
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN MODE - No changes will be made" "Yellow"
    }
    
    # Get current version
    $currentVersion = $null
    
    if ($UseGitTags) {
        $currentVersion = Get-VersionFromGitTags
    }
    
    if (-not $currentVersion) {
        $currentVersion = Get-CurrentVersion -ProjectPath $ProjectPath
    }
    
    Write-ColorOutput "Current version: $currentVersion" "White"
    
    # Calculate next version
    $nextVersion = Get-NextVersion -CurrentVersion $currentVersion -VersionType $VersionType
    Write-ColorOutput "Next version: $nextVersion" "Green"
    
    # Update project file
    Update-ProjectVersion -ProjectPath $ProjectPath -NewVersion $nextVersion

    # Create Git tag if requested
    if ($CreateTag) {
        Create-GitTag -Version $nextVersion
    }

    Write-ColorOutput "Version increment completed successfully!" "Green"
    
    # Output the new version for use in CI/CD
    Write-Output "NEW_VERSION=$nextVersion"
}
catch {
    Write-ColorOutput "Error: $($_.Exception.Message)" "Red"
    exit 1
}
