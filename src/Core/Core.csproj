﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <PackageId>HighCapital.Core</PackageId>
    <Version>2.0.0</Version>
    <Description>Uma biblioteca de setup do core da High Capital para reutilização entre microserviços</Description>
    <PackageTags>highcapital;core;microservices;dotnet</PackageTags>
    <IncludeBuildOutput>true</IncludeBuildOutput>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <!-- Include project references with PrivateAssets=all to include them in the package -->
  <ItemGroup>
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" PrivateAssets="all" />
  </ItemGroup>

  <!-- Include dependencies as NuGet package references -->
  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.12.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
    <PackageReference Include="Scalar.AspNetCore" Version="2.6.9" />
  </ItemGroup>

  <!-- Custom target to include project reference assemblies in the package -->
  <Target Name="IncludeReferencedProjectsInPackage" BeforeTargets="GenerateNuspec">
    <ItemGroup>
      <_PackageFiles Include="$(OutputPath)Infrastructure.dll" PackagePath="lib\$(TargetFramework)\Infrastructure.dll" />
      <_PackageFiles Include="$(OutputPath)Domain.dll" PackagePath="lib\$(TargetFramework)\Domain.dll" />
    </ItemGroup>
  </Target>

</Project>
