using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;

namespace HighCapital.Core.Dependencies;

public static class AuthenticationSetup
{
    public static IServiceCollection AddJWTAuthentication(
        this IServiceCollection services
    )
    {
        services
            .AddAuthentication(cfg =>
            {
                cfg.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                cfg.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(cfg =>
            {
                cfg.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.ASCII.GetBytes("8d6a34fb224a840117aa69e119f11d443d9f64022c6dfec2")
                    ),
                    ValidateIssuer = true,
                    ValidIssuer = "HighCapital",
                    ValidateAudience = true,
                    ValidAudience = "HighCapital",
                    ValidateLifetime = true,
                };
            });
        return services;
    }
}
