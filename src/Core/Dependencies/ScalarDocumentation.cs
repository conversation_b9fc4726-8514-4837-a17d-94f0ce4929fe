using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Scalar.AspNetCore;
using Microsoft.OpenApi.Models;

namespace HighCapital.Core.Dependencies;

public static class ScalarDocumentationSetup
{
    public static string DocumentationPath = "/docs";
    public static string DocumentationName = "API Docs";
    public static string DocumentationDescription = "API documentation from HighCapital";

    public static void updateDocumentationConfig(string name, string description, string path)
    {
        DocumentationPath = path;
        DocumentationName = name;
        DocumentationDescription = description;
    }
    public static IServiceCollection AddOpenApiDocumentation(
        this IServiceCollection services
    )
    {

        services.AddOpenApi(options =>
        {
            options.AddDocumentTransformer((document, context, cancellationToken) =>
            {
                document.Info = new OpenApiInfo
                {
                    Title = DocumentationName,
                    Version = "v1",
                    Description = DocumentationDescription
                };

                // Add security scheme
                document.Components ??= new OpenApiComponents();
                document.Components.SecuritySchemes ??= new Dictionary<string, OpenApiSecurityScheme>();

                document.Components.SecuritySchemes["Bearer"] = new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer",
                    BearerFormat = "JWT",
                    Description = "Please enter a valid token"
                };

                return Task.CompletedTask;
            });

            options.AddOperationTransformer((operation, context, cancellationToken) =>
            {
                // Add Bearer token security to all operations
                operation.Security =
                [
                    new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            Array.Empty<string>()
                        }
                    }
                ];
                return Task.CompletedTask;
            });
        });

        return services;
    }
    
    public static WebApplication UseOpenApiAndScalarDocumentation(this WebApplication app)
    {
        app.MapOpenApi();
        app.MapScalarApiReference(DocumentationPath, options =>
        {
            options.Title = DocumentationName;
            options.Theme = ScalarTheme.BluePlanet;
            options.Servers = new[]
            {
                new ScalarServer(app.Configuration.GetValue("DeploymentUrl", "https://localhost:5000"),
                    "Production server")
            };
            options.DefaultHttpClient = new(ScalarTarget.CSharp, ScalarClient.HttpClient);
        });
        return app;
    }
}
