using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace HighCapital.Core.Dependencies;

public static class ConfigureCors
{
    public static IServiceCollection AddCoreCorsPolicy(this IServiceCollection services)
    {
        services.AddCors(options =>
        {
            options.AddPolicy(
                "AllowLocalhostsReact",
                policy =>
                {
                    policy
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .WithOrigins("https://high-copt-web-dev-681379300571.us-east1.run.app")
                        .WithOrigins("https://high-agents-web-dev-681379300571.us-east1.run.app")
                        .WithOrigins("http://localhost:5173")
                        .WithOrigins("http://localhost:5000")
                        .WithOrigins("http://localhost:3000");
                }
            );

            options.AddPolicy(
                "AllowAll",
                policy =>
                {
                    policy.AllowAnyMethod().AllowAnyHeader().AllowAnyOrigin();
                }
            );
        });
        return services;
    }

    public static WebApplication UseCoreCorsPolicy(
        this WebApplication app,
        string policyName = "AllowLocalhostsReact"
    )
    {
        app.UseCors(policyName);
        return app;
    }
}
