using HighCapital.Core.Interfaces;

namespace HighCapital.Core.Services;

public class HashService : IHashService
{
    public string Sha256(string input)
    {
        var bytes = System.Text.Encoding.UTF8.GetBytes(input);
        var hash = System.Security.Cryptography.SHA256.HashData(bytes);
        return Convert.ToHexString(hash).ToLowerInvariant();
    }

    public string HmacSha256(string input, string key)
    {
        var bytes = System.Text.Encoding.UTF8.GetBytes(input);
        using var hmac = new System.Security.Cryptography.HMACSHA256(
            System.Text.Encoding.UTF8.GetBytes(key)
        );
        var hash = hmac.ComputeHash(bytes);
        return Convert.ToHexString(hash).ToLowerInvariant();
    }

    //var sign = HashUtils.HmacSha256("data", "secret-key")
}