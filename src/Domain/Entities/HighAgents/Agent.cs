using HighCapital.Core.Domain.Entities;

namespace HighCapital.Core.Domain.HighAgents.Entities
{
    public class Agent
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public required string Context { get; set; }

        public required int UserId { get; set; }
        public User? User { get; set; }

        public required int? AgentParamsId { get; set; }
        public AgentParams? AgentParams { get; set; }

        public string? InstanceWhatsappName { get; set; }
        public ICollection<Message>? Messages { get; set; }
    }
}
