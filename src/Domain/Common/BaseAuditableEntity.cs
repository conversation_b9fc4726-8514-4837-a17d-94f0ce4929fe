using HighCapital.Core.Domain.Common;

namespace HighCapital.Core.Domain.Common;

public abstract class BaseAuditableEntity : BaseEntity
{
    public DateTimeOffset CreatedAt { get; set; }

    public string? CreatedBy { get; set; }

    public DateTimeOffset UpdatedAt { get; set; }

     public string? UpdatedBy { get; set; }

    // public string? DeletedBy { get; set; } // quem (id)

    // public DateTimeOffset? DeletedAt { get; set; } // quando
}