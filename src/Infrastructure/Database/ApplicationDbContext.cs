using System.Reflection;
using HighCapital.Core.Domain.Entities;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Mapping;

namespace HighCapital.Core.Infrastructure.Database;

public class CoreDbContext : DbContext
{
    public CoreDbContext(DbContextOptions<CoreDbContext> options)
        : base(options) { }

    public DbSet<User> Users { get; set; } = null!;
    public DbSet<Auth> Auths { get; set; } = null!;

    //HighAgents
    public DbSet<Agent> Agents { get; set; } = null!;
    public DbSet<Message> Messages { get; set; } = null!;
    public DbSet<AgentParams> AgentParams { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Mapeamento de User
        builder.Entity<User>(entity =>
        {
            entity.ToTable("Users"); // nome da tabela no banco

            entity.HasKey(u => u.Id);

            entity.Property(u => u.ExternalId)
                  .IsRequired();

            entity.Property(u => u.Name)
                  .HasMaxLength(150)
                  .IsRequired();

            entity.Property(u => u.Email)
                  .HasMaxLength(150)
                  .IsRequired();

            entity.Property(u => u.Phone)
                  .HasMaxLength(20)
                  .IsRequired();

            entity.Property(u => u.Password)
                  .HasMaxLength(200)
                  .IsRequired();

            entity.HasIndex(u => u.Email).IsUnique();
        });


        builder.Entity<Auth>(entity =>
        {
            entity.ToTable("Auths");

            entity.HasKey(a => a.Id);

            entity.Property(a => a.Id)
                  .ValueGeneratedOnAdd();

            entity.Property(a => a.UserId)
                  .IsRequired();

            // Exemplo: FK com User
            entity.HasOne<User>()
                  .WithMany()
                  .HasForeignKey(a => a.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        //HighAgents
        builder.Entity<Agent>(new AgentEntityMap().Configure);
        builder.Entity<Message>(new MessageEntityMap().Configure);
        builder.Entity<AgentParams>(new AgentParamsEntityMap().Configure);


        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
