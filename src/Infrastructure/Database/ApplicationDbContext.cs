using System.Reflection;
using HighCapital.Core.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace HighCapital.Core.Infrastructure.Database;

public class CoreDbContext : DbContext
{
    public CoreDbContext(DbContextOptions<CoreDbContext> options)
        : base(options) { }

    public DbSet<User> Users => Set<User>();
    public DbSet<Role> Roles => Set<Role>();
    public DbSet<Permissions> Permissions => Set<Permissions>();
    public DbSet<Auth> Auths => Set<Auth>();

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
