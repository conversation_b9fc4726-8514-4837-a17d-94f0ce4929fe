using HighCapital.Core.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace HighCapital.Core.Infrastructure.Repository
{
    public class BaseRepository<T> where T : class
    {
        protected readonly CoreDbContext _context;
        private readonly DbSet<T> _dataset;

        public BaseRepository(CoreDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _dataset = _context.Set<T>();
        }

        public async Task<T> InsertAsync(T item)
        {
            _dataset.Add(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var entity = await _dataset.FindAsync(id);
            if (entity == null) return false;

            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<T?> UpdateAsync(T item, Guid id)
        {
            var existing = await _dataset.FindAsync(id);
            if (existing == null) return null;

            _context.Entry(existing).CurrentValues.SetValues(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<bool> ExistAsync(Guid id)
        {
            return await _dataset.FindAsync(id) != null;
        }

        public async Task<T?> SelectAsync(Guid id)
        {
            return await _dataset.FindAsync(id);
        }

        public async Task<IEnumerable<T>> SelectAsync()
        {
            return await _dataset.ToListAsync();
        }
    }
}
