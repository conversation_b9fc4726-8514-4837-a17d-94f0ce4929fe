using HighCapital.Core.Domain.Entities;
using HighCapital.Core.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;


namespace HighCapital.Core.Infrastructure.Repository
{
    public class UserRepository : BaseRepository<User>
    {
        public UserRepository(CoreDbContext context) : base(context) { }
        
        public async Task<User?> GetByEmailAsync(string email)
        {
             return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.Email == email);
        }
    }
}