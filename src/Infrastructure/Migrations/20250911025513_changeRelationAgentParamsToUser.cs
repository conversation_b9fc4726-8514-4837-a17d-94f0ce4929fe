﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class changeRelationAgentParamsToUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AgentParamsHighAgents_AgentsHighAgents_AgentId",
                table: "AgentParamsHighAgents");

            migrationBuilder.RenameColumn(
                name: "AgentId",
                table: "AgentParamsHighAgents",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "IX_AgentParamsHighAgents_AgentId",
                table: "AgentParamsHighAgents",
                newName: "IX_AgentParamsHighAgents_UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_AgentParamsHighAgents_Users_UserId",
                table: "AgentParamsHighAgents",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AgentParamsHighAgents_Users_UserId",
                table: "AgentParamsHighAgents");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AgentParamsHighAgents",
                newName: "AgentId");

            migrationBuilder.RenameIndex(
                name: "IX_AgentParamsHighAgents_UserId",
                table: "AgentParamsHighAgents",
                newName: "IX_AgentParamsHighAgents_AgentId");

            migrationBuilder.AddForeignKey(
                name: "FK_AgentParamsHighAgents_AgentsHighAgents_AgentId",
                table: "AgentParamsHighAgents",
                column: "AgentId",
                principalTable: "AgentsHighAgents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
