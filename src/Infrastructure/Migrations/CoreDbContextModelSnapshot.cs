﻿// <auto-generated />
using System;
using HighCapital.Core.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Infrastructure.Migrations
{
    [DbContext(typeof(CoreDbContext))]
    partial class CoreDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("HighCapital.Core.Domain.Entities.Auth", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("RefreshToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Auths", (string)null);
                });

            modelBuilder.Entity("HighCapital.Core.Domain.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<Guid>("ExternalId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.Agent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AgentParamsId")
                        .HasColumnType("integer");

                    b.Property<string>("Context")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("InstanceWhatsappName")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AgentParamsId");

                    b.HasIndex("UserId");

                    b.ToTable("AgentsHighAgents", (string)null);
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.AgentParams", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AlternativeSuggestionStyle")
                        .HasColumnType("text");

                    b.Property<string>("AskAvailabilityStyle")
                        .HasColumnType("text");

                    b.Property<string>("CallToAction")
                        .HasColumnType("text");

                    b.Property<string>("CallToActionScript")
                        .HasColumnType("text");

                    b.Property<string>("CompanyDescription")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("ConfirmationStyle")
                        .HasColumnType("text");

                    b.Property<string>("ContextDescription")
                        .HasColumnType("text");

                    b.Property<string>("ConversationGuidelines")
                        .HasColumnType("text");

                    b.Property<string>("CourtesyMessage")
                        .HasColumnType("text");

                    b.Property<string>("DisqualifiedFlowScript")
                        .HasColumnType("text");

                    b.Property<string>("EmotionalActivationScript")
                        .HasColumnType("text");

                    b.Property<string>("FinalQualificationQuestions")
                        .HasColumnType("text");

                    b.Property<string>("Goals")
                        .HasColumnType("text");

                    b.Property<string>("MainMission")
                        .HasColumnType("text");

                    b.Property<string>("OpeningScript")
                        .HasColumnType("text");

                    b.Property<string>("OpportunityReinforcementScript")
                        .HasColumnType("text");

                    b.Property<string>("PainAgitationScript")
                        .HasColumnType("text");

                    b.Property<string>("ParamName")
                        .HasColumnType("text");

                    b.Property<string>("PreQualificationQuestions")
                        .HasColumnType("text");

                    b.Property<string>("PricingAgitationScript")
                        .HasColumnType("text");

                    b.Property<string>("QualificationRules")
                        .HasColumnType("text");

                    b.Property<string>("RecurrenceStyle")
                        .HasColumnType("text");

                    b.Property<string>("ReminderStyle")
                        .HasColumnType("text");

                    b.Property<string>("ReminderTiming")
                        .HasColumnType("text");

                    b.Property<string>("RestrictionsAndLimits")
                        .HasColumnType("text");

                    b.Property<string>("SolutionScript")
                        .HasColumnType("text");

                    b.Property<string>("Tone")
                        .HasColumnType("text");

                    b.Property<string>("TraditionalMethods")
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserTone")
                        .HasColumnType("text");

                    b.Property<string>("ValueGenerationScript")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AgentParamsHighAgents", (string)null);
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.Message", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AgentId")
                        .HasColumnType("integer");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ConversationIdentificator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.ToTable("MessagesHighAgent", (string)null);
                });

            modelBuilder.Entity("HighCapital.Core.Domain.Entities.Auth", b =>
                {
                    b.HasOne("HighCapital.Core.Domain.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.Agent", b =>
                {
                    b.HasOne("HighCapital.Core.Domain.HighAgents.Entities.AgentParams", "AgentParams")
                        .WithMany()
                        .HasForeignKey("AgentParamsId");

                    b.HasOne("HighCapital.Core.Domain.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AgentParams");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.AgentParams", b =>
                {
                    b.HasOne("HighCapital.Core.Domain.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.Message", b =>
                {
                    b.HasOne("HighCapital.Core.Domain.HighAgents.Entities.Agent", null)
                        .WithMany("Messages")
                        .HasForeignKey("AgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("HighCapital.Core.Domain.HighAgents.Entities.Agent", b =>
                {
                    b.Navigation("Messages");
                });
#pragma warning restore 612, 618
        }
    }
}
