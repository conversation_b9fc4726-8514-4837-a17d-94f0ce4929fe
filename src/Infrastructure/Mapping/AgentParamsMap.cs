using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HighCapital.Core.Infrastructure.Mapping
{
    public class AgentParamsEntityMap : IEntityTypeConfiguration<AgentParams>
    {
        public void Configure(EntityTypeBuilder<AgentParams> builder)
        {
            builder.ToTable("AgentParamsHighAgents");

            builder.HasKey(p => p.Id);


            builder.Property(p => p.ParamName).IsRequired(false);

            // Identidade e Persona
            builder.Property(p => p.CompanyName).IsRequired(false);
            builder.Property(p => p.CompanyDescription).IsRequired(false);
            builder.Property(p => p.Tone).IsRequired(false);
            builder.Property(p => p.Goals).IsRequired(false);
            builder.Property(p => p.MainMission).IsRequired(false);
            builder.Property(p => p.ContextDescription).IsRequired(false);
            builder.Property(p => p.QualificationRules).IsRequired(false);

            // Scripts e Framework do Agente
            builder.Property(p => p.ConversationGuidelines).IsRequired(false);
            builder.Property(p => p.OpeningScript).IsRequired(false);
            builder.Property(p => p.PreQualificationQuestions).IsRequired(false);
            builder.Property(p => p.PainAgitationScript).IsRequired(false);
            builder.Property(p => p.PricingAgitationScript).IsRequired(false);
            builder.Property(p => p.TraditionalMethods).IsRequired(false);
            builder.Property(p => p.SolutionScript).IsRequired(false);
            builder.Property(p => p.ValueGenerationScript).IsRequired(false);
            builder.Property(p => p.FinalQualificationQuestions).IsRequired(false);
            builder.Property(p => p.OpportunityReinforcementScript).IsRequired(false);
            builder.Property(p => p.EmotionalActivationScript).IsRequired(false);
            builder.Property(p => p.CallToActionScript).IsRequired(false);
            builder.Property(p => p.DisqualifiedFlowScript).IsRequired(false);
            builder.Property(p => p.RestrictionsAndLimits).IsRequired(false);

            // Agendamento e mensagens automáticas
            builder.Property(p => p.AskAvailabilityStyle).IsRequired(false);
            builder.Property(p => p.ConfirmationStyle).IsRequired(false);
            builder.Property(p => p.UserTone).IsRequired(false);
            builder.Property(p => p.AlternativeSuggestionStyle).IsRequired(false);
            builder.Property(p => p.ReminderStyle).IsRequired(false);
            builder.Property(p => p.ReminderTiming).IsRequired(false);
            builder.Property(p => p.RecurrenceStyle).IsRequired(false);
            builder.Property(p => p.CallToAction).IsRequired(false);
            builder.Property(p => p.CourtesyMessage).IsRequired(false);

            // Relacionamento com User
            builder.Property(p => p.UserId).IsRequired(true);
        }
    }
}
