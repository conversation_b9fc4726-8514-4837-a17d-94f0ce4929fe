
using HighCapital.Core.Domain.Entities;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HighCapital.Core.Infrastructure.Mapping
{
    public class AgentEntityMap : IEntityTypeConfiguration<Agent>
    {
        public void Configure(EntityTypeBuilder<Agent> builder)
        {
            builder.ToTable("AgentsHighAgents");
            builder.HasKey(p => p.Id);
            builder.Property(u => u.Name).IsRequired();
            builder.Property(u => u.Context).IsRequired();
            builder.Property(u => u.UserId).IsRequired();
            builder.Property(u => u.InstanceWhatsappName).IsRequired(false);
            builder.Property(u => u.AgentParamsId)
                   .IsRequired(false);
        }

    }
}
