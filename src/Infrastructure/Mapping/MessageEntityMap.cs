
using HighCapital.Core.Domain.Entities;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HighCapital.Core.Infrastructure.Mapping
{
    public class MessageEntityMap : IEntityTypeConfiguration<Message>
    {
        public void Configure(EntityTypeBuilder<Message> builder)
        {
            builder.ToTable("MessagesHighAgent");
            builder.HasKey(p => p.Id);
            builder.Property(u => u.AgentId).IsRequired();
            builder.Property(u => u.Content).IsRequired();
            builder.Property(u => u.Role).IsRequired();
            builder.Property(u => u.ConversationIdentificator).IsRequired();
        }

    }
}
