# HighCapital.Core

Este é o pacote principal para as aplicações da HighCapital. Ele fornece as principais funcionalidades, entidades de domínio e migrações de banco de dados.

## Visão Geral

O `HighCapital.Core` é um pacote NuGet projetado para simplificar o desenvolvimento de novas aplicações dentro do ecossistema HighCapital. Ele inclui:

-   **Core**: Interfaces e serviços essenciais.
-   **Domain**: Entidades e lógica de domínio.
-   **Infrastructure**: Implementação de serviços, acesso a dados e migrações do Entity Framework.

## Instalação

Para instalar o pacote, use o .NET CLI:

```bash
dotnet add package HighCapital.Core --version <version>
```

Ou adicione-o através do NuGet Package Manager no Visual Studio.

## Uso

Após instalar o pacote, você precisará configurar o `DbContext` em seu arquivo `Program.cs` ou `Startup.cs`:

```csharp
// Exemplo de configuração para o DbContext
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")))
```

### Aplicando Migrações

Para aplicar as migrações ao seu banco de dados, você pode usar o .NET Entity Framework Core CLI:

```bash
dotnet ef database update
```

Isso aplicará todas as migrações pendentes do pacote `HighCapital.Core` ao banco de dados de destino.

## Contribuição

Pull requests são bem-vindos. Para mudanças importantes, por favor, abra uma issue primeiro para discutir o que você gostaria de mudar.

