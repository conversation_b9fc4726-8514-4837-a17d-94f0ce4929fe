name: Create Tag on Version Merge

on:
  push:
    branches:
      - main
    paths:
      - 'src/Core/Core.csproj'

permissions:
  contents: write

jobs:
  create-tag:
    runs-on: ubuntu-latest
    if: contains(github.event.head_commit.message, 'chore bump version to')
    steps:
      - uses: actions/checkout@v4

      - name: Extract version and create tag
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Get version from project file
          VERSION=$(grep -oP '<Version>\K[^<]+' src/Core/Core.csproj)
          echo "Found version: $VERSION"

          # Configure git
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"

          # Create and push tag
          TAG_NAME="v$VERSION"
          echo "Creating tag: $TAG_NAME"

          # Check if tag already exists
          if git rev-parse "$TAG_NAME" >/dev/null 2>&1; then
            echo "Tag $TAG_NAME already exists, skipping"
          else
            git tag "$TAG_NAME"
            git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}.git
            git push origin "$TAG_NAME"
            echo "Tag $TAG_NAME created and pushed"
          fi
