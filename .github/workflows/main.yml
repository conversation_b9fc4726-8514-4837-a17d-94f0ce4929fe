name: Publish NuGet

on:
  push:
    branches:
     - main
    tags:
      - "v*"
  pull_request:
    branches:
      - main
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version increment type'
        required: false
        default: 'auto'
        type: choice
        options:
        - auto
        - patch
        - minor
        - major

permissions:
  contents: write
  packages: write
  pull-requests: write
  actions: read

env:
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  DOTNET_NOLOGO: true
  NuGetDirectory: ${{github.workspace}}/nuget

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ steps.version.outputs.new_version }}
      should_publish: ${{ steps.version.outputs.should_publish }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"

      - name: Increment Version
        id: version
        run: |
          # Get current version from project file
          CURRENT_VERSION=$(grep -oP '<Version>\K[^<]+' src/Core/Core.csproj)
          echo "Current version: $CURRENT_VERSION"

          # Determine version type
          VERSION_TYPE="${{ github.event.inputs.version_type }}"
          if [ -z "$VERSION_TYPE" ]; then
            VERSION_TYPE="auto"
          fi

          # Parse current version
          IFS='.' read -r MAJOR MINOR PATCH <<< "$CURRENT_VERSION"

          # Determine increment based on commits if auto
          if [ "$VERSION_TYPE" = "auto" ]; then
            # Get recent commits since last push
            COMMITS=$(git log --oneline -10 --pretty=format:"%s")
            echo "Analyzing commits:"
            echo "$COMMITS" | head -5

            # Check for breaking changes (major)
            if echo "$COMMITS" | grep -qE '(BREAKING|breaking change|major|feat!|fix!)'; then
              VERSION_TYPE="major"
              echo "Detected: Breaking changes -> major increment"
            # Check for features (minor)
            elif echo "$COMMITS" | grep -qE '(feat:|feature:|add:|new:|implement:)'; then
              VERSION_TYPE="minor"
              echo "Detected: New features -> minor increment"
            # Check for fixes (patch)
            elif echo "$COMMITS" | grep -qE '(fix:|bug:|resolve:|patch:|correct:)'; then
              VERSION_TYPE="patch"
              echo "Detected: Bug fixes -> patch increment"
            else
              VERSION_TYPE="patch"
              echo "Default: patch increment"
            fi
          fi

          # Calculate new version
          case $VERSION_TYPE in
            "major")
              NEW_VERSION="$((MAJOR + 1)).0.0"
              ;;
            "minor")
              NEW_VERSION="$MAJOR.$((MINOR + 1)).0"
              ;;
            "patch")
              NEW_VERSION="$MAJOR.$MINOR.$((PATCH + 1))"
              ;;
          esac

          echo "Version type: $VERSION_TYPE"
          echo "New version: $NEW_VERSION"

          # Update project file
          sed -i "s/<Version>$CURRENT_VERSION<\/Version>/<Version>$NEW_VERSION<\/Version>/" src/Core/Core.csproj

          # Verify the change
          UPDATED_VERSION=$(grep -oP '<Version>\K[^<]+' src/Core/Core.csproj)
          echo "Updated version in file: $UPDATED_VERSION"

          # Set outputs
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "should_publish=true" >> $GITHUB_OUTPUT

      - name: Commit version changes
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        env:
          # Use PAT with bypass permissions for protected branch
          GITHUB_TOKEN: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
        run: |
          # Configure git with GitHub Actions bot
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"

          # Check if there are changes to commit
          git add src/Core/Core.csproj

          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            echo "Committing version change to ${{ steps.version.outputs.new_version }}"
            git commit -m "chore: bump version to ${{ steps.version.outputs.new_version }} [skip ci]"

            # Push with token authentication (using PAT if available)
            git remote set-url origin https://x-access-token:${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}.git
            git push origin HEAD:main
          fi

      - name: Create and push tag
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        env:
          # Use PAT with bypass permissions for protected branch
          GITHUB_TOKEN: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
        run: |
          # Configure git
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"

          # Create and push tag
          TAG_NAME="v${{ steps.version.outputs.new_version }}"
          echo "Creating tag: $TAG_NAME"

          git tag "$TAG_NAME"
          git remote set-url origin https://x-access-token:${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}.git
          git push origin "$TAG_NAME"
      
      # - name: Install EF Core CLI
      #   run: dotnet tool install --global dotnet-ef

      - name: Restore dependencies
        run: dotnet restore src/Core/Core.csproj

      - name: Build all projects (Domain, Infrastructure, Core)
        run: |
          dotnet build src/Domain/Domain.csproj --configuration Release --no-restore
          dotnet build src/Infrastructure/Infrastructure.csproj --configuration Release --no-restore
          dotnet build src/Core/Core.csproj --configuration Release --no-restore
      
      # - name: Run EF Migrations
      #   env:
      #     ConnectionStrings__DefaultConnection: ${{ secrets.DB_CONNECTION }}
      #   run: |
      #     dotnet ef database update \
      #       --project src/Infrastructure/Infrastructure.csproj \
      #       --startup-project src/Infrastructure/Infrastructure.csproj \
      #       --connection "$ConnectionStrings__DefaultConnection"

          
      - name: Create NuGet directory
        run: mkdir -p ${{ env.NuGetDirectory }}

      - name: Pack NuGet package
        run: dotnet pack src/Core/Core.csproj --configuration Release --output ${{ env.NuGetDirectory }} --no-build

      - name: Upload NuGet package as artifact
        uses: actions/upload-artifact@v4
        with:
          name: nuget-packages
          if-no-files-found: error
          retention-days: 7
          path: ${{ env.NuGetDirectory }}/*.nupkg

  migrate:
    runs-on: ubuntu-latest
    needs: [ build ]
    if: needs.build.outputs.should_publish == 'true' && (github.event_name == 'push' && github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch')
    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"

      - name: Install EF Core CLI
        run: dotnet tool install --global dotnet-ef

      - name: Restore dependencies
        run: dotnet restore src/Core/Core.csproj

      - name: Build all projects (Domain, Infrastructure, Core)
        run: |
          dotnet build src/Domain/Domain.csproj --configuration Release --no-restore
          dotnet build src/Infrastructure/Infrastructure.csproj --configuration Release --no-restore
          dotnet build src/Core/Core.csproj --configuration Release --no-restore
      
      - name: Run EF Migrations
        env:
          ConnectionStrings__DefaultConnection: ${{ secrets.DB_CONNECTION }}
        run: |
          dotnet ef database update \
            --project src/Infrastructure/Infrastructure.csproj \
            --startup-project src/Infrastructure/Infrastructure.csproj \
            --connection "$ConnectionStrings__DefaultConnection"

  publish:
    runs-on: ubuntu-latest
    needs: [ build, migrate ]
    if: needs.build.outputs.should_publish == 'true' && (github.event_name == 'push' && github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v') || github.event_name == 'workflow_dispatch')
    steps:
      - name: Download NuGet package artifact
        uses: actions/download-artifact@v4
        with:
          name: nuget-packages
          path: ${{ env.NuGetDirectory }}

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"

      - name: Publish to GitHub Packages
        env:
          NEW_VERSION: ${{ needs.build.outputs.new_version }}
        run: |
          echo "Publishing version: $NEW_VERSION"
          for file in ${{ env.NuGetDirectory }}/*.nupkg
          do
            echo "Publishing: $file"
            dotnet nuget push "$file" --api-key ${{ secrets.NUGET_TOKEN }} --source "https://nuget.pkg.github.com/HighCapitalTech/index.json" --skip-duplicate
          done

      - name: Create GitHub Release
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ needs.build.outputs.new_version }}
          release_name: Release v${{ needs.build.outputs.new_version }}
          body: |
            ## Changes in v${{ needs.build.outputs.new_version }}

            This release was automatically generated.

            ### Package Information
            - **Package**: HighCapital.Core
            - **Version**: ${{ needs.build.outputs.new_version }}
            - **Published**: ${{ github.event.head_commit.timestamp }}

            ### Installation
            ```bash
            dotnet add package HighCapital.Core --version ${{ needs.build.outputs.new_version }}
            ```
          draft: false
          prerelease: false
